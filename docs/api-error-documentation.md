# API Error Documentation

This document provides comprehensive documentation of all possible errors that each API endpoint can throw, including errors from nested service calls.

## Error Response Format

All errors follow a standardized format:

```json
{
  "statusCode": 400,
  "message": "User not found",
  "error": "Bad Request",
  "errorCode": "USER_NOT_FOUND",
  "timestamp": "2023-12-01T10:00:00.000Z",
  "path": "/api/users/123",
  "validationErrors": {
    "email": ["Email is required"],
    "password": ["Password must be at least 8 characters"]
  }
}
```

## Error Codes

### Authentication & Authorization
- `INVALID_CREDENTIALS` - Invalid email or password
- `TOKEN_EXPIRED` - To<PERSON> has expired
- `INVALID_TOKEN` - Invalid token
- `UNAUTHORIZED` - Unauthorized access
- `FORBIDDEN` - Access forbidden
- `EMAIL_NOT_VERIFIED` - Email address is not verified
- `PHONE_NOT_VERIFIED` - Phone number is not verified

### User Management
- `USER_NOT_FOUND` - User not found
- `EMAIL_ALREADY_EXISTS` - Email address already exists
- `INVALID_PASSWORD` - Invalid password
- `PASSWORD_REQUIRED` - Password is required

### Partner Management
- `PARTNER_NOT_FOUND` - Partner not found
- `TAX_NUMBER_ALREADY_EXISTS` - Tax number already exists

### Worker Management
- `WORKER_NOT_FOUND` - Worker not found
- `WORKER_NOT_EMPLOYED` - Worker is not employed
- `WORKER_NOT_APPROVED` - Worker is not approved
- `WORKER_NOT_ASSIGNED_TO_PROJECT` - Worker is not assigned to a project
- `WORKER_ALREADY_PAUSED` - Worker is already paused
- `WORKER_ALREADY_ACTIVE` - Worker is already active
- `WORKER_ALREADY_FINISHED` - Worker has already finished their report

### Manager Management
- `MANAGER_NOT_FOUND` - Manager not found
- `INSUFFICIENT_MANAGER_PERMISSIONS` - Insufficient manager permissions

### Project Management
- `PROJECT_NOT_FOUND` - Project not found
- `PROJECT_ALREADY_EXISTS` - Project already exists

### Daily Reports
- `DAILY_REPORT_NOT_FOUND` - Daily report not found
- `ACTIVE_REPORT_EXISTS` - Cannot start a new report while another report is active
- `NO_ACTIVE_REPORT` - No active report found
- `REPORT_ON_PAUSE` - Worker has paused report
- `SUBMITTED_TIME_MISMATCH` - Submitted time differs significantly from calculated working time

### Registration
- `REGISTRATION_CODE_NOT_FOUND` - Registration code not found
- `REGISTRATION_CODE_EXPIRED` - Registration code has expired
- `REGISTRATION_CODE_INACTIVE` - Registration code is inactive
- `REGISTRATION_REQUEST_NOT_FOUND` - Registration request not found
- `INVALID_REGISTRATION_CODE` - Invalid registration code

### Files
- `FILE_NOT_FOUND` - File not found
- `INVALID_FILE_TYPE` - Invalid file type
- `FILE_TOO_LARGE` - File size exceeds maximum limit
- `FILE_UPLOAD_FAILED` - File upload failed

### General
- `VALIDATION_ERROR` - Validation error
- `INTERNAL_SERVER_ERROR` - Internal server error
- `BAD_REQUEST` - Bad request
- `CONFLICT` - Conflict
- `NOT_FOUND` - Resource not found

## Endpoint Error Documentation

### Authentication Endpoints

#### POST /auth/login
**Possible Errors:**
- `401 INVALID_CREDENTIALS` - Invalid email or password
- `400 VALIDATION_ERROR` - Invalid request body format

**Nested Service Calls:**
- UsersService.findByEmail() → `USER_NOT_FOUND`
- Password verification → `INVALID_PASSWORD`

#### POST /auth/logout
**Possible Errors:**
- `401 TOKEN_EXPIRED` - Token has expired
- `401 INVALID_TOKEN` - Invalid token
- `404 SESSION_NOT_FOUND` - Session not found

**Nested Service Calls:**
- SessionsService.findActive() → `SESSION_NOT_FOUND`

#### POST /auth/register/partner
**Possible Errors:**
- `409 EMAIL_ALREADY_EXISTS` - Email address already exists
- `409 TAX_NUMBER_ALREADY_EXISTS` - Tax number already exists
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- UsersService.findByEmail() → `EMAIL_ALREADY_EXISTS`
- PartnersService.findByTaxNumber() → `TAX_NUMBER_ALREADY_EXISTS`

#### POST /auth/register/worker
**Possible Errors:**
- `409 EMAIL_ALREADY_EXISTS` - Email address already exists
- `400 INVALID_REGISTRATION_CODE` - Invalid registration code
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- UsersService.findByEmail() → `EMAIL_ALREADY_EXISTS`
- RegistrationCodesService.check() → `INVALID_REGISTRATION_CODE`

#### POST /auth/register/manager
**Possible Errors:**
- `409 EMAIL_ALREADY_EXISTS` - Email address already exists
- `400 INVALID_REGISTRATION_CODE` - Invalid registration code
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- UsersService.findByEmail() → `EMAIL_ALREADY_EXISTS`
- RegistrationCodesService.check() → `INVALID_REGISTRATION_CODE`

#### POST /auth/refresh
**Possible Errors:**
- `401 TOKEN_EXPIRED` - Refresh token has expired
- `401 INVALID_TOKEN` - Invalid refresh token
- `400 VALIDATION_ERROR` - Invalid refresh token format

#### POST /auth/generate-password-reset-token
**Possible Errors:**
- `404 USER_NOT_FOUND` - User not found
- `400 VALIDATION_ERROR` - Invalid email address

**Nested Service Calls:**
- UsersService.findByEmail() → `USER_NOT_FOUND`

#### POST /auth/reset-password
**Possible Errors:**
- `404 RESET_TOKEN_NOT_FOUND` - Reset token not found
- `400 RESET_TOKEN_EXPIRED` - Reset token has expired
- `404 USER_NOT_FOUND` - User not found
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- PasswordResetRequestsService.findByToken() → `RESET_TOKEN_NOT_FOUND`
- UsersService.findOne() → `USER_NOT_FOUND`

### Daily Reports Endpoints

#### POST /daily-reports
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `400 WORKER_NOT_EMPLOYED` - Worker is not employed
- `400 WORKER_NOT_APPROVED` - Worker is not approved
- `400 WORKER_NOT_ASSIGNED_TO_PROJECT` - Worker is not assigned to a project
- `409 ACTIVE_REPORT_EXISTS` - Cannot start a new report while another report is active
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- WorkersService.findOne() → `WORKER_NOT_FOUND`, `WORKER_NOT_EMPLOYED`, `WORKER_NOT_APPROVED`, `WORKER_NOT_ASSIGNED_TO_PROJECT`
- DailyReportsService.findActiveByWorkerId() → `ACTIVE_REPORT_EXISTS`

#### PUT /daily-reports/:id/finish
**Possible Errors:**
- `404 DAILY_REPORT_NOT_FOUND` - Daily report not found
- `403 FORBIDDEN_TO_FINISH_REPORT` - Not authorized to finish this report
- `404 WORKER_NOT_FOUND` - Worker not found
- `400 WORKER_ALREADY_FINISHED` - Worker has already finished their report
- `400 SUBMITTED_TIME_MISMATCH` - Submitted time differs significantly from calculated working time
- `404 NO_ACTIVE_REPORT` - No active report found
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- DailyReportsService.findOneWithWorker() → `DAILY_REPORT_NOT_FOUND`
- WorkersService.findOne() → `WORKER_NOT_FOUND`, `WORKER_ALREADY_FINISHED`
- DailyReportsService.calculateValidWorkingTime() → `NO_ACTIVE_REPORT`

#### PUT /daily-reports/:id/change-pause
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `400 WORKER_ALREADY_PAUSED` - Worker is already paused
- `400 WORKER_ALREADY_ACTIVE` - Worker is already active
- `404 PAUSE_HISTORY_NOT_FOUND` - Pause history entry not found
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- WorkersService.findOne() → `WORKER_NOT_FOUND`, `WORKER_ALREADY_PAUSED`, `WORKER_ALREADY_ACTIVE`
- PauseHistoryService.findActiveByReportId() → `PAUSE_HISTORY_NOT_FOUND`

### Workers Endpoints

#### GET /workers
**Possible Errors:**
- `404 MANAGER_NOT_FOUND` - Manager not found
- `404 PARTNER_NOT_FOUND` - Partner not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 INSUFFICIENT_MANAGER_PERMISSIONS` - Insufficient manager permissions

**Nested Service Calls:**
- ManagersService.findOne() → `MANAGER_NOT_FOUND`

#### GET /workers/:id
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### PUT /workers/:id
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

#### PATCH /workers/me/quit
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `400 WORKER_ALREADY_TERMINATED` - Worker is already terminated
- `400 WORKER_ON_NOTICE` - Worker is currently on notice period
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### PATCH /workers/me/employer/:registrationCode
**Possible Errors:**
- `400 INVALID_REGISTRATION_CODE` - Invalid registration code
- `400 WORKER_ALREADY_EMPLOYED` - Worker is already employed
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

### Partners Endpoints

#### GET /partners/me
**Possible Errors:**
- `404 PARTNER_NOT_FOUND` - Partner not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### GET /partners/:id
**Possible Errors:**
- `404 PARTNER_NOT_FOUND` - Partner not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### PATCH /partners/me
**Possible Errors:**
- `404 PARTNER_NOT_FOUND` - Partner not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

#### PATCH /partners/me/workers/:workerId
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

#### PATCH /partners/me/workers/:workerId/fire
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `400 WORKER_ALREADY_TERMINATED` - Worker is already terminated
- `400 WORKER_ON_NOTICE` - Worker is currently on notice period
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### POST /partners/me/workers
**Possible Errors:**
- `409 EMAIL_ALREADY_EXISTS` - Email address already exists
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

#### POST /partners/me/managers
**Possible Errors:**
- `409 EMAIL_ALREADY_EXISTS` - Email address already exists
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

### Projects Endpoints

#### GET /projects
**Possible Errors:**
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### GET /projects/:projectId
**Possible Errors:**
- `404 PROJECT_NOT_FOUND` - Project not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### POST /projects
**Possible Errors:**
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

#### PATCH /projects/:projectId
**Possible Errors:**
- `404 PROJECT_NOT_FOUND` - Project not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

#### DELETE /projects/:projectId
**Possible Errors:**
- `404 PROJECT_NOT_FOUND` - Project not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `409 RESOURCE_IN_USE` - Project has active workers or reports

#### POST /projects/:projectId/workers/:workerId
**Possible Errors:**
- `404 PROJECT_NOT_FOUND` - Project not found
- `404 WORKER_NOT_FOUND` - Worker not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### DELETE /projects/:projectId/workers/:workerId
**Possible Errors:**
- `404 PROJECT_NOT_FOUND` - Project not found
- `404 WORKER_NOT_FOUND` - Worker not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

### Files Endpoints

#### POST /files/upload
**Possible Errors:**
- `400 UNSUPPORTED_FILE_FORMAT` - Invalid file type
- `400 FILE_SIZE_EXCEEDED` - File size exceeds maximum limit
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

#### GET /files/:id
**Possible Errors:**
- `404 FILE_NOT_FOUND` - File not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 INSUFFICIENT_FILE_PERMISSIONS` - Insufficient file permissions

#### PUT /workers/:id
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden
- `400 VALIDATION_ERROR` - Invalid input data

**Nested Service Calls:**
- WorkersService.findOne() → `WORKER_NOT_FOUND`

#### PUT /workers/:id/terminate
**Possible Errors:**
- `404 WORKER_NOT_FOUND` - Worker not found
- `400 WORKER_ALREADY_TERMINATED` - Worker is already terminated
- `400 WORKER_ON_NOTICE` - Worker is currently on notice period to be terminated
- `401 UNAUTHORIZED` - Unauthorized access
- `403 FORBIDDEN` - Access forbidden

**Nested Service Calls:**
- WorkersService.findOneWithPartner() → `WORKER_NOT_FOUND`, `WORKER_ALREADY_TERMINATED`, `WORKER_ON_NOTICE`

## HTTP Status Code Mapping

- `400 Bad Request` - Validation errors, business logic violations
- `401 Unauthorized` - Authentication failures, invalid tokens
- `403 Forbidden` - Authorization failures, insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflicts (email exists, etc.)
- `500 Internal Server Error` - Unexpected server errors

## Best Practices

1. **Always check error codes** instead of relying on HTTP status codes alone
2. **Handle validation errors** by checking the `validationErrors` field
3. **Implement proper error handling** for all documented error scenarios
4. **Use error codes for internationalization** of error messages
5. **Log errors appropriately** based on their severity and type
