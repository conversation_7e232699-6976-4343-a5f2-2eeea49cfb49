import { ApiProperty } from '@nestjs/swagger';

import { ErrorCode } from '../exceptions/error-codes';

export class ErrorResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'User not found',
  })
  message: string;

  @ApiProperty({
    description: 'Error type',
    example: 'Bad Request',
  })
  error: string;

  @ApiProperty({
    description: 'Standardized error code',
    enum: ErrorCode,
    example: ErrorCode.USER_NOT_FOUND,
  })
  errorCode: ErrorCode;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2023-12-01T10:00:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Request path that caused the error',
    example: '/api/users/123',
  })
  path: string;

  @ApiProperty({
    description: 'Validation errors (only present for validation failures)',
    type: 'object',
    additionalProperties: {
      type: 'array',
      items: { type: 'string' },
    },
    required: false,
    example: {
      email: ['Email is required', 'Email must be valid'],
      password: ['Password must be at least 8 characters'],
    },
  })
  validationErrors?: Record<string, string[]>;
}

export class BadRequestErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({ example: 400 })
  statusCode: 400;

  @ApiProperty({ example: 'Bad Request' })
  error: 'Bad Request';
}

export class UnauthorizedErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({ example: 401 })
  statusCode: 401;

  @ApiProperty({ example: 'Unauthorized' })
  error: 'Unauthorized';
}

export class ForbiddenErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({ example: 403 })
  statusCode: 403;

  @ApiProperty({ example: 'Forbidden' })
  error: 'Forbidden';
}

export class NotFoundErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({ example: 404 })
  statusCode: 404;

  @ApiProperty({ example: 'Not Found' })
  error: 'Not Found';
}

export class ConflictErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({ example: 409 })
  statusCode: 409;

  @ApiProperty({ example: 'Conflict' })
  error: 'Conflict';
}

export class InternalServerErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({ example: 500 })
  statusCode: 500;

  @ApiProperty({ example: 'Internal Server Error' })
  error: 'Internal Server Error';

  @ApiProperty({ example: ErrorCode.INTERNAL_SERVER_ERROR })
  errorCode: ErrorCode.INTERNAL_SERVER_ERROR;

  @ApiProperty({ example: 'Internal server error' })
  message: 'Internal server error';
}

// Specific error response DTOs for common scenarios
export class ValidationErrorResponseDto extends BadRequestErrorResponseDto {
  @ApiProperty({ example: ErrorCode.VALIDATION_ERROR })
  errorCode: ErrorCode.VALIDATION_ERROR;

  @ApiProperty({ example: 'Validation failed' })
  message: 'Validation failed';

  @ApiProperty({
    example: {
      email: ['Email is required'],
      password: ['Password must be at least 8 characters'],
    },
  })
  validationErrors: Record<string, string[]>;
}

export class UserNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.USER_NOT_FOUND })
  errorCode: ErrorCode.USER_NOT_FOUND;

  @ApiProperty({ example: 'User not found' })
  message: 'User not found';
}

export class WorkerNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.WORKER_NOT_FOUND })
  errorCode: ErrorCode.WORKER_NOT_FOUND;

  @ApiProperty({ example: 'Worker not found' })
  message: 'Worker not found';
}

export class PartnerNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.PARTNER_NOT_FOUND })
  errorCode: ErrorCode.PARTNER_NOT_FOUND;

  @ApiProperty({ example: 'Partner not found' })
  message: 'Partner not found';
}

export class ProjectNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.PROJECT_NOT_FOUND })
  errorCode: ErrorCode.PROJECT_NOT_FOUND;

  @ApiProperty({ example: 'Project not found' })
  message: 'Project not found';
}

export class ManagerNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.MANAGER_NOT_FOUND })
  errorCode: ErrorCode.MANAGER_NOT_FOUND;

  @ApiProperty({ example: 'Manager not found' })
  message: 'Manager not found';
}

export class InvalidCredentialsErrorResponseDto extends UnauthorizedErrorResponseDto {
  @ApiProperty({ example: ErrorCode.INVALID_CREDENTIALS })
  errorCode: ErrorCode.INVALID_CREDENTIALS;

  @ApiProperty({ example: 'Invalid email or password' })
  message: 'Invalid email or password';
}

export class TokenExpiredErrorResponseDto extends UnauthorizedErrorResponseDto {
  @ApiProperty({ example: ErrorCode.TOKEN_EXPIRED })
  errorCode: ErrorCode.TOKEN_EXPIRED;

  @ApiProperty({ example: 'Token has expired' })
  message: 'Token has expired';
}

export class EmailAlreadyExistsErrorResponseDto extends ConflictErrorResponseDto {
  @ApiProperty({ example: ErrorCode.EMAIL_ALREADY_EXISTS })
  errorCode: ErrorCode.EMAIL_ALREADY_EXISTS;

  @ApiProperty({ example: 'Email address already exists' })
  message: 'Email address already exists';
}

export class InsufficientPermissionsErrorResponseDto extends ForbiddenErrorResponseDto {
  @ApiProperty({ example: ErrorCode.INSUFFICIENT_MANAGER_PERMISSIONS })
  errorCode: ErrorCode.INSUFFICIENT_MANAGER_PERMISSIONS;

  @ApiProperty({ example: 'Insufficient manager permissions' })
  message: 'Insufficient manager permissions';
}

export class FileNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.FILE_NOT_FOUND })
  errorCode: ErrorCode.FILE_NOT_FOUND;

  @ApiProperty({ example: 'File not found' })
  message: 'File not found';
}

export class InvalidFileTypeErrorResponseDto extends BadRequestErrorResponseDto {
  @ApiProperty({ example: ErrorCode.INVALID_FILE_TYPE })
  errorCode: ErrorCode.INVALID_FILE_TYPE;

  @ApiProperty({ example: 'Invalid file type' })
  message: 'Invalid file type';
}

export class DeviceNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.DEVICE_NOT_FOUND })
  errorCode: ErrorCode.DEVICE_NOT_FOUND;

  @ApiProperty({ example: 'Device not found' })
  message: 'Device not found';
}

export class CredentialVerificationNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.CREDENTIAL_VERIFICATION_NOT_FOUND })
  errorCode: ErrorCode.CREDENTIAL_VERIFICATION_NOT_FOUND;

  @ApiProperty({ example: 'Credential verification not found' })
  message: 'Credential verification not found';
}

export class PresenceValidationNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.PRESENCE_VALIDATION_NOT_FOUND })
  errorCode: ErrorCode.PRESENCE_VALIDATION_NOT_FOUND;

  @ApiProperty({ example: 'Presence validation not found' })
  message: 'Presence validation not found';
}

export class RegistrationCodeNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.REGISTRATION_CODE_NOT_FOUND })
  errorCode: ErrorCode.REGISTRATION_CODE_NOT_FOUND;

  @ApiProperty({ example: 'Registration code not found' })
  message: 'Registration code not found';
}

export class RegistrationRequestNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.REGISTRATION_REQUEST_NOT_FOUND })
  errorCode: ErrorCode.REGISTRATION_REQUEST_NOT_FOUND;

  @ApiProperty({ example: 'Registration request not found' })
  message: 'Registration request not found';
}

export class ActivityHistoryNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.ACTIVITY_HISTORY_NOT_FOUND })
  errorCode: ErrorCode.ACTIVITY_HISTORY_NOT_FOUND;

  @ApiProperty({ example: 'Activity history record not found' })
  message: 'Activity history record not found';
}

export class EmploymentHistoryNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.EMPLOYMENT_HISTORY_NOT_FOUND })
  errorCode: ErrorCode.EMPLOYMENT_HISTORY_NOT_FOUND;

  @ApiProperty({ example: 'Employment history record not found' })
  message: 'Employment history record not found';
}

export class PauseHistoryNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.PAUSE_HISTORY_NOT_FOUND })
  errorCode: ErrorCode.PAUSE_HISTORY_NOT_FOUND;

  @ApiProperty({ example: 'Pause history record not found' })
  message: 'Pause history record not found';
}

export class FilePermissionNotFoundErrorResponseDto extends NotFoundErrorResponseDto {
  @ApiProperty({ example: ErrorCode.FILE_PERMISSION_NOT_FOUND })
  errorCode: ErrorCode.FILE_PERMISSION_NOT_FOUND;

  @ApiProperty({ example: 'File permission not found' })
  message: 'File permission not found';
}
