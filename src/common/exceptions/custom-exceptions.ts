import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

import { ErrorCode, ErrorMessages } from './error-codes';

export interface StandardErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  errorCode: ErrorCode;
  timestamp: string;
  path: string;
  validationErrors?: Record<string, string[]>;
}

export class StandardHttpException extends HttpException {
  constructor(
    public readonly errorCode: ErrorCode,
    status: HttpStatus,
    message?: string,
  ) {
    super(message || ErrorMessages[errorCode], status);
  }

  getErrorCode(): ErrorCode {
    return this.errorCode;
  }
}

// Authentication & Authorization Exceptions
export class InvalidCredentialsException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_CREDENTIALS]);
    this.name = 'InvalidCredentialsException';
  }
}

export class TokenExpiredException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.TOKEN_EXPIRED]);
    this.name = 'TokenExpiredException';
  }
}

export class InvalidTokenException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_TOKEN]);
    this.name = 'InvalidTokenException';
  }
}

export class EmailNotVerifiedException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.EMAIL_NOT_VERIFIED]);
    this.name = 'EmailNotVerifiedException';
  }
}

// User Management Exceptions
export class UserNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.USER_NOT_FOUND]);
    this.name = 'UserNotFoundException';
  }
}

export class EmailAlreadyExistsException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.EMAIL_ALREADY_EXISTS]);
    this.name = 'EmailAlreadyExistsException';
  }
}

export class InvalidPasswordException extends UnauthorizedException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_PASSWORD]);
    this.name = 'InvalidPasswordException';
  }
}

export class PasswordRequiredException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.PASSWORD_REQUIRED]);
    this.name = 'PasswordRequiredException';
  }
}

// Partner Management Exceptions
export class PartnerNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PARTNER_NOT_FOUND]);
    this.name = 'PartnerNotFoundException';
  }
}

export class TaxNumberAlreadyExistsException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.TAX_NUMBER_ALREADY_EXISTS]);
    this.name = 'TaxNumberAlreadyExistsException';
  }
}

// Worker Management Exceptions
export class WorkerNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_NOT_FOUND]);
    this.name = 'WorkerNotFoundException';
  }
}

export class WorkerNotEmployedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_NOT_EMPLOYED]);
    this.name = 'WorkerNotEmployedException';
  }
}

export class WorkerNotApprovedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_NOT_APPROVED]);
    this.name = 'WorkerNotApprovedException';
  }
}

export class WorkerNotAssignedToProjectException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_NOT_ASSIGNED_TO_PROJECT]);
    this.name = 'WorkerNotAssignedToProjectException';
  }
}

export class WorkerAlreadyPausedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_ALREADY_PAUSED]);
    this.name = 'WorkerAlreadyPausedException';
  }
}

export class WorkerAlreadyActiveException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_ALREADY_ACTIVE]);
    this.name = 'WorkerAlreadyActiveException';
  }
}

export class WorkerAlreadyFinishedException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_ALREADY_FINISHED]);
    this.name = 'WorkerAlreadyFinishedException';
  }
}

// Manager Management Exceptions
export class ManagerNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.MANAGER_NOT_FOUND]);
    this.name = 'ManagerNotFoundException';
  }
}

export class InsufficientManagerPermissionsException extends ForbiddenException {
  constructor() {
    super(ErrorMessages[ErrorCode.INSUFFICIENT_MANAGER_PERMISSIONS]);
    this.name = 'InsufficientManagerPermissionsException';
  }
}

// Project Management Exceptions
export class ProjectNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PROJECT_NOT_FOUND]);
    this.name = 'ProjectNotFoundException';
  }
}

// Daily Reports Exceptions
export class DailyReportNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.DAILY_REPORT_NOT_FOUND]);
    this.name = 'DailyReportNotFoundException';
  }
}

export class ActiveReportExistsException extends ConflictException {
  constructor() {
    super(ErrorMessages[ErrorCode.ACTIVE_REPORT_EXISTS]);
    this.name = 'ActiveReportExistsException';
  }
}

export class NoActiveReportException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.NO_ACTIVE_REPORT]);
    this.name = 'NoActiveReportException';
  }
}

export class ReportOnPauseException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.REPORT_ON_PAUSE]);
    this.name = 'ReportOnPauseException';
  }
}

export class SubmittedTimeMismatchException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.SUBMITTED_TIME_MISMATCH]);
    this.name = 'SubmittedTimeMismatchException';
  }
}

// Presence Validations Exceptions
export class PresenceValidationNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.PRESENCE_VALIDATION_NOT_FOUND]);
    this.name = 'PresenceValidationNotFoundException';
  }
}

export class ValidationRequirementsMissingException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.VALIDATION_REQUIREMENTS_MISSING]);
    this.name = 'ValidationRequirementsMissingException';
  }
}

export class WorkerIdsEmptyException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.WORKER_IDS_EMPTY]);
    this.name = 'WorkerIdsEmptyException';
  }
}

// Registration Exceptions
export class RegistrationCodeNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.REGISTRATION_CODE_NOT_FOUND]);
    this.name = 'RegistrationCodeNotFoundException';
  }
}

export class RegistrationRequestNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.REGISTRATION_REQUEST_NOT_FOUND]);
    this.name = 'RegistrationRequestNotFoundException';
  }
}

// Files Exceptions
export class FileNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.FILE_NOT_FOUND]);
    this.name = 'FileNotFoundException';
  }
}

export class InvalidFileTypeException extends BadRequestException {
  constructor(allowedTypes?: string[]) {
    const message = allowedTypes 
      ? `${ErrorMessages[ErrorCode.INVALID_FILE_TYPE]}. Allowed types: ${allowedTypes.join(', ')}`
      : ErrorMessages[ErrorCode.INVALID_FILE_TYPE];
    super(message);
    this.name = 'InvalidFileTypeException';
  }
}

// Sessions Exceptions
export class SessionNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.SESSION_NOT_FOUND]);
    this.name = 'SessionNotFoundException';
  }
}

// Verification Exceptions
export class InvalidVerificationTypeException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_VERIFICATION_TYPE]);
    this.name = 'InvalidVerificationTypeException';
  }
}

// Device Exceptions
export class DeviceNotFoundException extends NotFoundException {
  constructor() {
    super(ErrorMessages[ErrorCode.DEVICE_NOT_FOUND]);
    this.name = 'DeviceNotFoundException';
  }
}

// Registration Code Exceptions
export class InvalidRegistrationCodeException extends BadRequestException {
  constructor() {
    super(ErrorMessages[ErrorCode.INVALID_REGISTRATION_CODE]);
    this.name = 'InvalidRegistrationCodeException';
  }
}

export class RegistrationCodeRequiredException extends BadRequestException {
  constructor() {
    super('Registration Code is required');
    this.name = 'RegistrationCodeRequiredException';
  }
}

// Password Reset Exceptions
export class PasswordResetTokenNotFoundException extends NotFoundException {
  constructor() {
    super('Password reset request not found');
    this.name = 'PasswordResetTokenNotFoundException';
  }
}

export class PasswordResetTokenExpiredException extends BadRequestException {
  constructor() {
    super('Password reset request expired');
    this.name = 'PasswordResetTokenExpiredException';
  }
}

// Additional exceptions for daily reports and other services
export class EmptyArrayException extends BadRequestException {
  constructor(message: string = 'Array is empty') {
    super(message);
    this.name = 'EmptyArrayException';
  }
}

export class SupervisorNotFoundException extends BadRequestException {
  constructor() {
    super('Supervisor not found');
    this.name = 'SupervisorNotFoundException';
  }
}

export class SupervisorMismatchException extends ForbiddenException {
  constructor() {
    super('Forbidden to change report status, supervisor mismatch');
    this.name = 'SupervisorMismatchException';
  }
}

export class ProjectNotManagedByManagerException extends ForbiddenException {
  constructor() {
    super('Forbidden to change report status, project not managed');
    this.name = 'ProjectNotManagedByManagerException';
  }
}

export class ForbiddenToFinishReportException extends ForbiddenException {
  constructor() {
    super('Not authorized to finish this report');
    this.name = 'ForbiddenToFinishReportException';
  }
}

export class ForbiddenToCreateManualReportException extends ForbiddenException {
  constructor() {
    super('Forbidden to create manual report (project not managed)');
    this.name = 'ForbiddenToCreateManualReportException';
  }
}

export class PauseHistoryNotFoundException extends NotFoundException {
  constructor() {
    super('Pause history entry not found');
    this.name = 'PauseHistoryNotFoundException';
  }
}
