export enum ErrorCode {
  // Authentication & Authorization
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  PHONE_NOT_VERIFIED = 'PHONE_NOT_VERIFIED',
  
  // User Management
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  PASSWORD_REQUIRED = 'PASSWORD_REQUIRED',
  
  // Partner Management
  PARTNER_NOT_FOUND = 'PARTNER_NOT_FOUND',
  TAX_NUMBER_ALREADY_EXISTS = 'TAX_NUMBER_ALREADY_EXISTS',
  
  // Worker Management
  WORKER_NOT_FOUND = 'WORKER_NOT_FOUND',
  WORKER_NOT_EMPLOYED = 'WORKER_NOT_EMPLOYED',
  WORKER_NOT_APPROVED = 'WORKER_NOT_APPROVED',
  WORKER_NOT_ASSIGNED_TO_PROJECT = 'WORKER_NOT_ASSIGNED_TO_PROJECT',
  WORKER_ALREADY_PAUSED = 'WORKER_ALREADY_PAUSED',
  WORKER_ALREADY_ACTIVE = 'WORKER_ALREADY_ACTIVE',
  WORKER_ALREADY_FINISHED = 'WORKER_ALREADY_FINISHED',
  
  // Manager Management
  MANAGER_NOT_FOUND = 'MANAGER_NOT_FOUND',
  INSUFFICIENT_MANAGER_PERMISSIONS = 'INSUFFICIENT_MANAGER_PERMISSIONS',
  
  // Project Management
  PROJECT_NOT_FOUND = 'PROJECT_NOT_FOUND',
  PROJECT_ALREADY_EXISTS = 'PROJECT_ALREADY_EXISTS',
  
  // Daily Reports
  DAILY_REPORT_NOT_FOUND = 'DAILY_REPORT_NOT_FOUND',
  ACTIVE_REPORT_EXISTS = 'ACTIVE_REPORT_EXISTS',
  NO_ACTIVE_REPORT = 'NO_ACTIVE_REPORT',
  REPORT_ON_PAUSE = 'REPORT_ON_PAUSE',
  SUBMITTED_TIME_MISMATCH = 'SUBMITTED_TIME_MISMATCH',
  
  // Presence Validations
  PRESENCE_VALIDATION_NOT_FOUND = 'PRESENCE_VALIDATION_NOT_FOUND',
  VALIDATION_REQUIREMENTS_MISSING = 'VALIDATION_REQUIREMENTS_MISSING',
  WORKER_IDS_EMPTY = 'WORKER_IDS_EMPTY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Registration
  REGISTRATION_CODE_NOT_FOUND = 'REGISTRATION_CODE_NOT_FOUND',
  REGISTRATION_CODE_EXPIRED = 'REGISTRATION_CODE_EXPIRED',
  REGISTRATION_CODE_INACTIVE = 'REGISTRATION_CODE_INACTIVE',
  REGISTRATION_REQUEST_NOT_FOUND = 'REGISTRATION_REQUEST_NOT_FOUND',
  INVALID_REGISTRATION_CODE = 'INVALID_REGISTRATION_CODE',
  
  // Files
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  FILE_UPLOAD_FAILED = 'FILE_UPLOAD_FAILED',
  
  // Devices
  DEVICE_NOT_FOUND = 'DEVICE_NOT_FOUND',
  
  // Sessions
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  
  // Verification
  VERIFICATION_TOKEN_NOT_FOUND = 'VERIFICATION_TOKEN_NOT_FOUND',
  VERIFICATION_TOKEN_EXPIRED = 'VERIFICATION_TOKEN_EXPIRED',
  INVALID_VERIFICATION_TYPE = 'INVALID_VERIFICATION_TYPE',
  
  // Password Reset
  RESET_TOKEN_NOT_FOUND = 'RESET_TOKEN_NOT_FOUND',
  RESET_TOKEN_EXPIRED = 'RESET_TOKEN_EXPIRED',
  
  // General
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  CONFLICT = 'CONFLICT',
  NOT_FOUND = 'NOT_FOUND',
}

export const ErrorMessages: Record<ErrorCode, string> = {
  // Authentication & Authorization
  [ErrorCode.INVALID_CREDENTIALS]: 'Invalid email or password',
  [ErrorCode.TOKEN_EXPIRED]: 'Token has expired',
  [ErrorCode.INVALID_TOKEN]: 'Invalid token',
  [ErrorCode.UNAUTHORIZED]: 'Unauthorized access',
  [ErrorCode.FORBIDDEN]: 'Access forbidden',
  [ErrorCode.EMAIL_NOT_VERIFIED]: 'Email address is not verified',
  [ErrorCode.PHONE_NOT_VERIFIED]: 'Phone number is not verified',
  
  // User Management
  [ErrorCode.USER_NOT_FOUND]: 'User not found',
  [ErrorCode.EMAIL_ALREADY_EXISTS]: 'Email address already exists',
  [ErrorCode.INVALID_PASSWORD]: 'Invalid password',
  [ErrorCode.PASSWORD_REQUIRED]: 'Password is required',
  
  // Partner Management
  [ErrorCode.PARTNER_NOT_FOUND]: 'Partner not found',
  [ErrorCode.TAX_NUMBER_ALREADY_EXISTS]: 'Tax number already exists',
  
  // Worker Management
  [ErrorCode.WORKER_NOT_FOUND]: 'Worker not found',
  [ErrorCode.WORKER_NOT_EMPLOYED]: 'Worker is not employed',
  [ErrorCode.WORKER_NOT_APPROVED]: 'Worker is not approved',
  [ErrorCode.WORKER_NOT_ASSIGNED_TO_PROJECT]: 'Worker is not assigned to a project',
  [ErrorCode.WORKER_ALREADY_PAUSED]: 'Worker is already paused',
  [ErrorCode.WORKER_ALREADY_ACTIVE]: 'Worker is already active',
  [ErrorCode.WORKER_ALREADY_FINISHED]: 'Worker has already finished their report',
  
  // Manager Management
  [ErrorCode.MANAGER_NOT_FOUND]: 'Manager not found',
  [ErrorCode.INSUFFICIENT_MANAGER_PERMISSIONS]: 'Insufficient manager permissions',
  
  // Project Management
  [ErrorCode.PROJECT_NOT_FOUND]: 'Project not found',
  [ErrorCode.PROJECT_ALREADY_EXISTS]: 'Project already exists',
  
  // Daily Reports
  [ErrorCode.DAILY_REPORT_NOT_FOUND]: 'Daily report not found',
  [ErrorCode.ACTIVE_REPORT_EXISTS]: 'Cannot start a new report while another report is active',
  [ErrorCode.NO_ACTIVE_REPORT]: 'No active report found',
  [ErrorCode.REPORT_ON_PAUSE]: 'Worker has paused report',
  [ErrorCode.SUBMITTED_TIME_MISMATCH]: 'Submitted time differs significantly from calculated working time',
  
  // Presence Validations
  [ErrorCode.PRESENCE_VALIDATION_NOT_FOUND]: 'Presence validation not found',
  [ErrorCode.VALIDATION_REQUIREMENTS_MISSING]: 'At least one validation is required (photo or geo)',
  [ErrorCode.WORKER_IDS_EMPTY]: 'Worker IDs cannot be empty',
  [ErrorCode.RATE_LIMIT_EXCEEDED]: 'Rate limit exceeded for presence validation requests',
  
  // Registration
  [ErrorCode.REGISTRATION_CODE_NOT_FOUND]: 'Registration code not found',
  [ErrorCode.REGISTRATION_CODE_EXPIRED]: 'Registration code has expired',
  [ErrorCode.REGISTRATION_CODE_INACTIVE]: 'Registration code is inactive',
  [ErrorCode.REGISTRATION_REQUEST_NOT_FOUND]: 'Registration request not found',
  [ErrorCode.INVALID_REGISTRATION_CODE]: 'Invalid registration code',
  
  // Files
  [ErrorCode.FILE_NOT_FOUND]: 'File not found',
  [ErrorCode.INVALID_FILE_TYPE]: 'Invalid file type',
  [ErrorCode.FILE_TOO_LARGE]: 'File size exceeds maximum limit',
  [ErrorCode.FILE_UPLOAD_FAILED]: 'File upload failed',
  
  // Devices
  [ErrorCode.DEVICE_NOT_FOUND]: 'Device not found',
  
  // Sessions
  [ErrorCode.SESSION_NOT_FOUND]: 'Session not found',
  
  // Verification
  [ErrorCode.VERIFICATION_TOKEN_NOT_FOUND]: 'Verification token not found',
  [ErrorCode.VERIFICATION_TOKEN_EXPIRED]: 'Verification token has expired',
  [ErrorCode.INVALID_VERIFICATION_TYPE]: 'Invalid verification type',
  
  // Password Reset
  [ErrorCode.RESET_TOKEN_NOT_FOUND]: 'Reset token not found',
  [ErrorCode.RESET_TOKEN_EXPIRED]: 'Reset token has expired',
  
  // General
  [ErrorCode.VALIDATION_ERROR]: 'Validation error',
  [ErrorCode.INTERNAL_SERVER_ERROR]: 'Internal server error',
  [ErrorCode.BAD_REQUEST]: 'Bad request',
  [ErrorCode.CONFLICT]: 'Conflict',
  [ErrorCode.NOT_FOUND]: 'Resource not found',
};
