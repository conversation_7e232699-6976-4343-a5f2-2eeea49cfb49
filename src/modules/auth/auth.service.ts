import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { add, isAfter } from 'date-fns';
import { Request } from 'express';

import { Role } from '@/common/enums';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { generateId } from '@/common/utils/generateId';
import {
  hashPassword,
  hashTokenConsistently,
  verifyPassword,
} from '@/common/utils/hash';

import { CredentialVerificationService } from '../credential-verification/credential-verification.service';
import { DevicesService } from '../devices/devices.service';
import { ManagersService } from '../managers/managers.service';
import { PartnersService } from '../partners/partners.service';
import { PasswordResetRequestsService } from '../password-reset-requests/password-reset-requests.service';
import { RegistrationCodesService } from '../registration-codes/registration-codes.service';
import { RegistrationRequestsService } from '../registration-requests/registration-requests.service';
import { SessionsService } from '../sessions/sessions.service';
import { UsersService } from '../users/users.service';
import { WorkersService } from '../workers/workers.service';
import { BasicRegisterDto } from './dto/basic-register.dto';
import { GeneratePasswordResetTokenDto } from './dto/generate-password-reset-token.dto';
import { PasswordResetDto } from './dto/password-reset.dto';
import { ManagerRegisterDto } from './dto/register-manager.dto';
import { PartnerRegisterDto } from './dto/register-partner.dto';
import { WorkerRegisterDto } from './dto/register-worker.dto';
import { LocalStrategyReturnType } from './dto/request-user.dto';
import { TokenPayloadType } from './dto/token-payload.dto';
import { SecurityNotificationsService } from '../security-notifications/security-notifications.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly partnersService: PartnersService,
    private readonly managersService: ManagersService,
    private readonly workersService: WorkersService,
    private readonly registrationCodesService: RegistrationCodesService,
    private readonly registrationRequestsService: RegistrationRequestsService,
    private readonly passwordResetRequestsService: PasswordResetRequestsService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly devicesService: DevicesService,
    private readonly sessionsService: SessionsService,
    private readonly credentialVerificationService: CredentialVerificationService,
    private readonly securityNotificationsService: SecurityNotificationsService,
  ) {}

  async validateUser(validationDto: { credential: string; password: string }) {
    let user = null;
    if (validationDto.credential.includes('@')) {
      user = await this.usersService.findByEmail(
        validationDto.credential.toLowerCase(),
        {
          includeSensitiveData: true,
        },
      );
    } else {
      user = await this.usersService.findByPhoneNumber(
        validationDto.credential,
        {
          includeSensitiveData: true,
        },
      );
    }

    if (
      user &&
      user.deletedAt === null &&
      (await verifyPassword(user.hashedPassword, validationDto.password))
    ) {
      return user;
    }
    return null;
  }

  async login(requestUser: LocalStrategyReturnType, request: Request) {
    const sessionId = generateId();

    const tokenPayload: TokenPayloadType = {
      id: requestUser.id,
      sessionId,
    };

    const refreshToken = this.jwtService.sign(tokenPayload, {
      expiresIn: '30d',
      secret: this.configService.get('auth.jwtRefreshTokenSecret'),
    });

    const accessToken = this.jwtService.sign(tokenPayload);

    const session = await this.sessionsService.findActiveByDevice(
      requestUser.id,
      requestUser.deviceId,
    );

    if (session) {
      await this.sessionsService.deactivate(session.id);
    }

    await this.sessionsService.create({
      id: sessionId,
      userId: requestUser.id,
      deviceId: requestUser.deviceId,
      userRole: requestUser.role,
      hashedRefreshToken: await hashPassword(refreshToken),
      userAgent: request.get('user-agent'),
      ipAddress: request.get('x-forwarded-for'),
    });

    return {
      id: requestUser.id,
      entityId: requestUser.entityId,
      role: requestUser.role,
      sessionId,
      accessToken: accessToken,
      refreshToken: refreshToken,
    };
  }

  async logout(sessionId: string) {
    const session = await this.sessionsService.findActive(sessionId);
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    await this.sessionsService.deactivate(sessionId);
    await this.devicesService.disownDevice(session.deviceId, session.userId);
  }

  async basicRegister(registerDto: BasicRegisterDto, role: Role) {
    const user = await this.usersService.findByEmail(registerDto.email);
    if (user) {
      throw new ConflictException('Email already exists');
    }
    const hashedPassword = await hashPassword(registerDto.password);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, email, ...newUser } = registerDto;
    const { id: userId } = await this.usersService.create({
      ...newUser,
      hashedPassword,
      role,
      email: email.toLowerCase(),
    });

    await this.credentialVerificationService.createAndSendEmail({
      userId: userId,
      type: 'email',
      contact: email,
    });

    // await this.credentialVerificationService.createAndSendSms({
    //   userId: userId,
    //   type: 'phone',
    //   contact: newUser.phoneNumber,
    // });

    return { userId };
  }

  async partnerRegister(registerDto: PartnerRegisterDto) {
    const partnerCheck = await this.partnersService.findByTaxNumber(
      registerDto.taxNumber,
    );
    if (partnerCheck) {
      throw new ConflictException('Tax Number already exists');
    }

    const { userId } = await this.basicRegister(registerDto, Role.Partner);

    const partner = await this.partnersService.create({
      ...registerDto,
      userId,
    });

    await this.usersService.setEntityId(userId, partner.id);

    return {
      id: partner.id,
      userId,
    };
  }

  async workerRegister(registerDto: WorkerRegisterDto) {
    if (!registerDto.registrationCode)
      throw new BadRequestException('Registration Code is required');

    const registrationCodeCheck = await this.registrationCodesService.check({
      code: registerDto.registrationCode,
      role: 'worker',
    });

    if (!registrationCodeCheck.valid) {
      throw new BadRequestException('Invalid registration code');
    }

    const { userId } = await this.basicRegister(registerDto, Role.Worker);

    const worker = await this.workersService.create({
      ...registerDto,
      managerId: registrationCodeCheck.data.managerId,
      partnerId: registrationCodeCheck.data.partnerId,
      profession: registrationCodeCheck.data.profession,
      userId,
    });

    await this.usersService.setEntityId(userId, worker.id);

    await this.registrationRequestsService.create({
      registrationCodeId: registrationCodeCheck.data.id,
      requestedAt: new Date(),
      workerId: worker.id,
    });

    return {
      id: worker.id,
      userId,
    };
  }

  async managerRegister(registerDto: ManagerRegisterDto) {
    const registrationCodeCheck = await this.registrationCodesService.check({
      code: registerDto.registrationCode,
      role: 'manager',
    });

    if (!registrationCodeCheck.valid) {
      throw new BadRequestException('Invalid registration code');
    }

    const { userId } = await this.basicRegister(registerDto, Role.Manager);

    const manager = await this.managersService.create({
      ...registerDto,
      partnerId: registrationCodeCheck.data.partnerId,
      userId,
      permissionType:
        (registrationCodeCheck.data
          .managerPermissionType as ManagerPermissionType | null) ||
        ManagerPermissionType.ProjectManager,
    });

    await this.usersService.setEntityId(userId, manager.id);

    await this.registrationRequestsService.create({
      registrationCodeId: registrationCodeCheck.data.id,
      requestedAt: new Date(),
      managerId: manager.id,
    });

    return {
      id: manager.id,
      userId,
    };
  }

  async validateUserRefreshToken(refreshToken: string, sessionId: string) {
    const session = await this.sessionsService.findActive(sessionId);

    if (
      session &&
      (await verifyPassword(session.hashedRefreshToken, refreshToken))
    ) {
      return await this.usersService.findOne(session.userId, {
        includeSensitiveData: true,
      });
    }
    return null;
  }

  async generatePasswordResetToken({ email }: GeneratePasswordResetTokenDto) {
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const token = generateId();
    const hashedToken = await hashTokenConsistently(token);
    const expiresAt = add(new Date(), { hours: 1 });

    await this.passwordResetRequestsService.create({
      hashedToken,
      userId: user.id,
      expiresAt,
    });

    return token;
  }

  async resetPassword(passwordResetDto: PasswordResetDto) {
    const passwordResetRequest =
      await this.passwordResetRequestsService.findByToken(
        passwordResetDto.token,
      );

    if (!passwordResetRequest) {
      throw new NotFoundException('Password reset request not found');
    }

    if (isAfter(new Date(), passwordResetRequest.expiresAt)) {
      throw new BadRequestException('Password reset request expired');
    }

    const user = await this.usersService.findOne(passwordResetRequest.userId, {
      includeSensitiveData: true,
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const hashedPassword = await hashPassword(passwordResetDto.password);

    await this.usersService.update(user.id, {
      hashedPassword,
    });

    if (user.email) {
      await this.securityNotificationsService.sendSecurityNotification(
        user.email,
        'password_changed',
      );
    }

    await this.passwordResetRequestsService.markAsUsed(passwordResetRequest.id);
  }
}
